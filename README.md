# Enhanced Points Hook with Loyalty System

An advanced "points hook" with loyalty multipliers for Uniswap v4

This hook extends the basic points system with a **loyalty multiplier system** that rewards frequent users with bonus points based on their swap activity.

## Features

### 🎯 Core Functionality
- Awards points for ETH → TOKEN swaps (20% of ETH spent)
- ERC-1155 token representation for points
- Pool-specific point tracking

### 🏆 NEW: Loyalty Multiplier System
- **Bronze Tier** (Default): 1x multiplier
- **Silver Tier** (5+ swaps): 1.5x multiplier
- **Gold Tier** (15+ swaps): 2x multiplier
- Automatic tier upgrades based on swap frequency
- Transparent user stats and tier information

> This demonstrates advanced hook development concepts while maintaining backward compatibility.

## Quick Start

```bash
# Install dependencies
forge install

# Run tests
forge test -vv

# Build project
forge build
```

## Testing

The project includes comprehensive tests covering:
- ✅ Basic point mechanics
- ✅ Loyalty tier progression
- ✅ Multiplier calculations
- ✅ Edge cases and error handling
- ✅ Multiple user scenarios

```bash
# Run specific test
forge test --match-test test_loyaltyTierProgression -vv

# Run with gas reporting
forge test --gas-report
```

### Design

1. **Point Calculation**: 20% of ETH spent in swaps
   - Base points = ETH amount / 5
   - Multiplied by user's tier multiplier

2. **Point Representation**: ERC-1155 tokens
   - Pool-specific token IDs
   - Supports multiple pools per hook

3. **Loyalty System**: Tier-based multipliers
   - Tracks user swap frequency
   - Automatic tier upgrades
   - Transparent progression system

---

### beforeSwap vs afterSwap

this balancedelta thing is actually quite important for us

we're giving out points as a % of the amount of ETH that was spent in the swap

how much ETH was spent in the swap?

this is not a question that can be answered in `beforeSwap` because it is literally unknown until the swap happens

1. potentially, slippage limits could hit causing only a partial swap to happen
   e.g. Alice could've said sell 1 ETH for TOKEN, but slippage limit is hit, and only 0.5 ETH was actually swapped

2. there are broadly two types of swaps that uniswap can do. these are referred to as exact-input and exact-output swaps.

e.g. ETH/USDC pool. Alice wants to swap ETH for USDC.

exact input variant = Sell 1 ETH for USDC
e.g. she is "exactly" specifying how much ETH to sell, but not specifying how much USDC to get back

exact output variant = Sell UP TO 1 ETH for exactly 1500 USDC
e.g. she is "exactly" specifying how much USDC she wants back, and only a upper limit on how much ETH she's willing to spend

---

the "BalanceDelta" thing we have in `afterSwap` becomes very crucial to our use case
because `BalanceDelta` => the exact amount of tokens that need to be transferred (how much ETH was spent, how much TOKEN to withdraw)

Tl;DR: we gotta use `afterSwap` because we do not know how much ETH Alice spent before the swap happens

### minting points

who do we actually mint points to

does Uniswap (or our hook) have any idea who tf Alice is?
do we have Alice's address?

Alice -> Router -> PoolManager
-> msg.sender = Router
-> Hook.afterSwap(sender)
sender = Router address
msg.sender = Pool Manager

ok we cannot use `sender` or `msg.sender`

maybe we can use `tx.origin`. is that true?

if Alice is using an account abstracted wallet (SC wallet)

`tx.origin` = address of the relayer

GENERAL PURPOSE: `tx.origin` doesnt work either

---

how tf do we figure out who to mint points to

we're gonna ask the user to give us an address to mint points to (optionally)

if they dont specify an address/invalid address = dont mint any points

#### hookData

hookData allows users to pass in arbitrary information meant for use by the hook contract

Alice -> Router.swap(...., hookData) -> PoolManager.swap(...., hookData) -> HookContract.before..(..., hookData)

the hook contract can figure out what it wants to do with that hookData

in our case, we're gonna use this as a way to ask the user for an address

to illustrate the concept a bit better, a couple examples of better ways to use hookData

e.g. KYC hook for example
verify a ZK Proof that somebody is actually a verified human (World App ZK Proof)
hook only lets you swap/become an LP if youre a human

ZK Proof => hookData

#### BalanceDelta

effectively, for all intents and purposes, you can think of BalanceDelta as a struct with two values

```
struct BalanceDelta {
    int128 amount0;
    int128 amount1;
}
```

for a given operation (e.g. a swap) the related `BalanceDelta` contains amounts of token0 and token1 that need to be moved around

`amount0` => amount of token0
`amount1` => amount of token1

NOTE: these amounts are `int`s and NOT `uint`s
i.e. these can be negative numbers

in fact, in case of a swap, one of them will always be a negative number

there's a convention that's followed in uniswap

where everytime we talk about "money changing hands", we represent money coming in to uniswap and money going out of uniswap based on the sign of the numeric value

this "direction" of a token transfer is represented from the perspective of the caller to uniswap

+ve number => money is coming in to user's wallet (i.e. money is leaving Uniswap)
-ve number => money is leaving user's wallet (i.e. money is entering Uniswap)

in the case of a Swap where youre exchanging one token for another

imagine ETH/USDC pool, selling ETH for USDC, ETH is token0, USDC is token1

```
BalanceDelta {
    amount0 = some negative number (amount of ETH being swapped),
    amount1 = some positive number (amount of USDC being swapped)
}
```

in the case of Adding Liquidity to a pool,

(under the asumption you are adding both tokens as liquidity)

amount0 = -ve
amount1 = -ve
