// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import {BaseHook} from "v4-periphery/src/utils/BaseHook.sol";
import {ERC1155} from "solmate/src/tokens/ERC1155.sol";

import {Currency} from "v4-core/types/Currency.sol";
import {PoolKey} from "v4-core/types/PoolKey.sol";
import {PoolId} from "v4-core/types/PoolId.sol";
import {BalanceDelta} from "v4-core/types/BalanceDelta.sol";
import {SwapParams, ModifyLiquidityParams} from "v4-core/types/PoolOperation.sol";

import {IPoolManager} from "v4-core/interfaces/IPoolManager.sol";

import {Hooks} from "v4-core/libraries/Hooks.sol";

contract PointsHook is BaseHook, ERC1155 {
    // Loyalty system state variables
    mapping(address => uint256) public userSwapCounts;
    mapping(address => uint8) public userTiers; // 0 = Bronze, 1 = Silver, 2 = Gold

    // Tier thresholds
    uint256 public constant SILVER_THRESHOLD = 5;
    uint256 public constant GOLD_THRESHOLD = 15;

    // Tier multipliers (in basis points, 10000 = 1x)
    uint256 public constant BRONZE_MULTIPLIER = 10000; // 1x
    uint256 public constant SILVER_MULTIPLIER = 15000; // 1.5x
    uint256 public constant GOLD_MULTIPLIER = 20000;   // 2x

    // Events
    event TierUpgraded(address indexed user, uint8 newTier, uint256 swapCount);
    event PointsAwarded(address indexed user, uint256 poolId, uint256 basePoints, uint256 bonusPoints, uint8 tier);

    constructor(IPoolManager _manager) BaseHook(_manager) {}

    function getHookPermissions()
        public
        pure
        override
        returns (Hooks.Permissions memory)
    {
        return
            Hooks.Permissions({
                beforeInitialize: false,
                afterInitialize: false,
                beforeAddLiquidity: false,
                beforeRemoveLiquidity: false,
                afterAddLiquidity: false,
                afterRemoveLiquidity: false,
                beforeSwap: false,
                afterSwap: true,
                beforeDonate: false,
                afterDonate: false,
                beforeSwapReturnDelta: false,
                afterSwapReturnDelta: false,
                afterAddLiquidityReturnDelta: false,
                afterRemoveLiquidityReturnDelta: false
            });
    }

    function uri(uint256) public view virtual override returns (string memory) {
        return "https://api.example.com/token/{id}";
    }

    // View functions for loyalty system
    function getUserTier(address user) public view returns (uint8) {
        return userTiers[user];
    }

    function getUserSwapCount(address user) public view returns (uint256) {
        return userSwapCounts[user];
    }

    function getTierMultiplier(uint8 tier) public pure returns (uint256) {
        if (tier == 0) return BRONZE_MULTIPLIER;
        if (tier == 1) return SILVER_MULTIPLIER;
        if (tier == 2) return GOLD_MULTIPLIER;
        return BRONZE_MULTIPLIER; // Default to bronze
    }

    function getTierName(uint8 tier) public pure returns (string memory) {
        if (tier == 0) return "Bronze";
        if (tier == 1) return "Silver";
        if (tier == 2) return "Gold";
        return "Bronze"; // Default to bronze
    }

    function _calculateTier(uint256 swapCount) internal pure returns (uint8) {
        if (swapCount >= GOLD_THRESHOLD) return 2; // Gold
        if (swapCount >= SILVER_THRESHOLD) return 1; // Silver
        return 0; // Bronze
    }

    function _afterSwap(
        address,
        PoolKey calldata key,
        SwapParams calldata swapParams,
        BalanceDelta delta,
        bytes calldata hookData
    ) internal override returns (bytes4, int128) {
        // If this is not an ETH-TOKEN pool with this hook attached, ignore
        if (!key.currency0.isAddressZero()) return (this.afterSwap.selector, 0);

        // We only mint points if user is buying TOKEN with ETH
        if (!swapParams.zeroForOne) return (this.afterSwap.selector, 0);

        // Mint points equal to 20% of the amount of ETH they spent
        // Since its a zeroForOne swap:
        // if amountSpecified < 0:
        //      this is an "exact input for output" swap
        //      amount of ETH they spent is equal to |amountSpecified|
        // if amountSpecified > 0:
        //      this is an "exact output for input" swap
        //      amount of ETH they spent is equal to BalanceDelta.amount0()

        uint256 ethSpendAmount = uint256(int256(-delta.amount0()));
        uint256 pointsForSwap = ethSpendAmount / 5;

        // Mint the points
        _assignPoints(key.toId(), hookData, pointsForSwap);

        return (this.afterSwap.selector, 0);
    }

    function _assignPoints(
        PoolId poolId,
        bytes calldata hookData,
        uint256 basePoints
    ) internal {
        // If no hookData is passed in, no points will be assigned to anyone
        if (hookData.length == 0) return;

        // Extract user address from hookData
        address user = abi.decode(hookData, (address));

        // If there is hookData but not in the format we're expecting and user address is zero
        // nobody gets any points
        if (user == address(0)) return;

        // Update user swap count
        userSwapCounts[user]++;

        // Calculate new tier based on updated swap count
        uint8 newTier = _calculateTier(userSwapCounts[user]);
        uint8 currentTier = userTiers[user];

        // Check for tier upgrade
        if (newTier > currentTier) {
            userTiers[user] = newTier;
            emit TierUpgraded(user, newTier, userSwapCounts[user]);
        }

        // Apply tier multiplier to base points
        uint256 multiplier = getTierMultiplier(userTiers[user]);
        uint256 totalPoints = (basePoints * multiplier) / 10000;
        uint256 bonusPoints = totalPoints - basePoints;

        // Mint points to the user
        uint256 poolIdUint = uint256(PoolId.unwrap(poolId));
        _mint(user, poolIdUint, totalPoints, "");

        // Emit points awarded event
        emit PointsAwarded(user, poolIdUint, basePoints, bonusPoints, userTiers[user]);
    }
}
